package com.starlink.vpn.ui.home

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.starlink.vpn.MainActivity
import com.starlink.vpn.R
import com.starlink.vpn.databinding.FragmentHomeBinding
import com.starlink.vpn.viewmodel.VpnViewModel

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var vpnViewModel: VpnViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        vpnViewModel = ViewModelProvider(requireActivity())[VpnViewModel::class.java]
        
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupObservers() {
        // User info
        vpnViewModel.userName.observe(viewLifecycleOwner) { name ->
            binding.userName.text = name
        }
        
        vpnViewModel.userType.observe(viewLifecycleOwner) { type ->
            binding.userType.text = type
        }
        
        // Connection state
        vpnViewModel.isConnected.observe(viewLifecycleOwner) { connected ->
            updateConnectionStatus(connected)
        }
        
        vpnViewModel.connectionTime.observe(viewLifecycleOwner) { seconds ->
            binding.connectionTime.text = vpnViewModel.formatConnectionTime(seconds)
        }
        
        vpnViewModel.selectedServer.observe(viewLifecycleOwner) { server ->
            binding.currentServer.text = server
        }
        
        // Membership
        vpnViewModel.membershipExpireDate.observe(viewLifecycleOwner) { expireDate ->
            expireDate?.let {
                binding.expiryDate.text = vpnViewModel.formatMembershipTime(it)
            }
        }
        
        vpnViewModel.membershipExpired.observe(viewLifecycleOwner) { expired ->
            if (expired) {
                binding.expiryDate.text = getString(R.string.expired)
                binding.expiryDate.setTextColor(resources.getColor(R.color.status_urgent, null))
                binding.expiryLabel.visibility = View.GONE
            }
        }
    }
    
    private fun setupClickListeners() {
        // Connect button
        binding.connectButton.setOnClickListener {
            if (vpnViewModel.isConnected.value == true) {
                vpnViewModel.disconnect()
                showToast("连接已断开")
            } else {
                if (vpnViewModel.membershipExpired.value == true) {
                    showMembershipExpiredDialog()
                } else {
                    vpnViewModel.connect()
                    showToast("正在连接...")
                }
            }
        }
        
        // Server select button
        binding.serverSelectButton.setOnClickListener {
            if (vpnViewModel.membershipExpired.value == true) {
                showMembershipExpiredDialog()
            } else {
                // Navigate to servers fragment
                (activity as? MainActivity)?.let { mainActivity ->
                    // TODO: Navigate to servers fragment
                    showToast("服务器选择功能")
                }
            }
        }
    }
    
    private fun updateConnectionStatus(connected: Boolean) {
        if (connected) {
            binding.statusLabel.text = getString(R.string.connected)
            binding.statusLabel.setTextColor(resources.getColor(R.color.status_connected, null))
            binding.connectionDot.setBackgroundResource(R.drawable.connection_dot_connected)
            binding.connectButton.text = getString(R.string.disconnect)
        } else {
            binding.statusLabel.text = getString(R.string.disconnected)
            binding.statusLabel.setTextColor(resources.getColor(R.color.status_disconnected, null))
            binding.connectionDot.setBackgroundResource(R.drawable.connection_dot_disconnected)
            binding.connectButton.text = getString(R.string.connect)
        }
    }
    
    private fun showMembershipExpiredDialog() {
        // TODO: Show membership expired dialog
        showToast("会员已到期，请升级到高级版")
    }
    
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
