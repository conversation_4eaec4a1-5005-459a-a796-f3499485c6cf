<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background_white"
            android:elevation="4dp"
            android:padding="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/invite_friends"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Decoration Blobs -->
        <View
            android:id="@+id/decoration_blob1"
            android:layout_width="200dp"
            android:layout_height="200dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="-50dp"
            android:alpha="0.1"
            android:background="@drawable/decoration_blob"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/header" />

        <View
            android:id="@+id/decoration_blob2"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginStart="-30dp"
            android:layout_marginBottom="20dp"
            android:alpha="0.1"
            android:background="@drawable/decoration_blob"
            app:layout_constraintBottom_toTopOf="@id/copy_link_button"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="40dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/header">

            <!-- QR Code -->
            <androidx.cardview.widget.CardView
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginVertical="40dp"
                android:elevation="20dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="20dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@color/background_white">

                    <!-- QR Code Pattern -->
                    <View
                        android:id="@+id/qr_pattern"
                        android:layout_width="160dp"
                        android:layout_height="160dp"
                        android:background="@drawable/qr_pattern"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:text="@string/invite_your_friends"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <!-- Subtitle -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="5dp"
                android:text="@string/get_premium_subscription"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <!-- Note -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="30dp"
                android:text="@string/invite_note"
                android:textColor="@color/text_hint"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- Copy Link Button -->
        <Button
            android:id="@+id/copy_link_button"
            style="@style/PrimaryButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="40dp"
            android:layout_marginBottom="40dp"
            android:text="@string/copy_link"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>
