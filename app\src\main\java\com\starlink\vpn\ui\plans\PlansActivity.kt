package com.starlink.vpn.ui.plans

import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.starlink.vpn.databinding.ActivityPlansBinding

class PlansActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPlansBinding
    private var selectedPlan = "1_month"
    private var totalPrice = "$3.99美元"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPlansBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupClickListeners()
        updatePlanSelection()
    }

    private fun setupClickListeners() {
        binding.backButton.setOnClickListener {
            finish()
        }

        binding.plan12Months.setOnClickListener {
            selectPlan("12_months", "$39.99美元")
        }

        binding.plan1Month.setOnClickListener {
            selectPlan("1_month", "$3.99美元")
        }

        binding.continueButton.setOnClickListener {
            proceedToPayment()
        }

        binding.termsLink.setOnClickListener {
            showToast("服务条款")
        }

        binding.privacyLink.setOnClickListener {
            showToast("隐私政策")
        }

        binding.supportLink.setOnClickListener {
            showToast("客户服务")
        }
    }

    private fun selectPlan(planId: String, price: String) {
        selectedPlan = planId
        totalPrice = price
        updatePlanSelection()
        binding.totalPrice.text = price
    }

    private fun updatePlanSelection() {
        // Reset all plan backgrounds
        binding.plan12Months.setCardBackgroundColor(getColor(android.R.color.white))
        binding.plan1Month.setCardBackgroundColor(getColor(android.R.color.white))

        // Highlight selected plan
        when (selectedPlan) {
            "12_months" -> {
                binding.plan12Months.setCardBackgroundColor(getColor(android.R.color.holo_blue_light))
            }
            "1_month" -> {
                binding.plan1Month.setCardBackgroundColor(getColor(android.R.color.holo_blue_light))
            }
        }
    }

    private fun proceedToPayment() {
        showToast("前往支付页面: $totalPrice")
        // Here you would typically integrate with a payment processor
        // For now, just show a success message and finish
        finish()
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
