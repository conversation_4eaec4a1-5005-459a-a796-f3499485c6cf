<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background_white"
            android:elevation="4dp"
            android:padding="20dp">

            <ImageButton
                android:id="@+id/back_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/text_secondary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:contentDescription="@string/back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:text="@string/plans"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/back_button"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Plans Illustration -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="40dp">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginEnd="10dp"
                android:src="@drawable/ic_file"
                android:tint="@color/primary_blue" />

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_star"
                android:tint="@color/premium_gold" />

        </LinearLayout>

        <!-- Plans List -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:orientation="vertical">

            <!-- 12 Months Plan -->
            <androidx.cardview.widget.CardView
                android:id="@+id/plan_12_months"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/premium_plan_background"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/plan_12_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/twelve_months"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="20dp"
                        android:gravity="center"
                        android:text="折合 $0.11/天/ 4 Device"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/plan_12_badge"
                        app:layout_constraintStart_toEndOf="@id/plan_12_duration"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/plan_12_badge"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/premium_badge"
                        android:paddingHorizontal="10dp"
                        android:paddingVertical="5dp"
                        android:text="⚡"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- 1 Month Plan -->
            <androidx.cardview.widget.CardView
                android:id="@+id/plan_1_month"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/selected_plan_background"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/plan_1_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/one_month"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="20dp"
                        android:gravity="center"
                        android:text="折合 $0.17/天/ 2 Device"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/plan_1_button"
                        app:layout_constraintStart_toEndOf="@id/plan_1_duration"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/plan_1_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/smart_choice_button"
                        android:paddingHorizontal="16dp"
                        android:paddingVertical="8dp"
                        android:text="@string/smart_choice"
                        android:textColor="@color/text_white"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Plan Total -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/total_price"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/total_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="$3.99美元"
                    android:textColor="@color/primary_blue"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Plan Note -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="30dp"
            android:layout_marginBottom="30dp"
            android:lineSpacingExtra="4dp"
            android:text="注意：感谢您选择和支持 星链 VPN。本次支付为一次性付款，绝无额外费用。如需任何协助，请随时联系技术支持团队。点击下方按钮前往支付页面完成交易。"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

        <!-- Continue Button -->
        <Button
            android:id="@+id/continue_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="30dp"
            android:layout_marginBottom="20dp"
            android:text="@string/continue_btn" />

        <!-- Plan Links -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="30dp"
            android:paddingBottom="30dp">

            <TextView
                android:id="@+id/terms_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:text="@string/terms_of_use"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="5dp"
                android:text="|"
                android:textColor="@color/text_hint"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/privacy_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:text="@string/privacy_policy"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="5dp"
                android:text="|"
                android:textColor="@color/text_hint"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/support_link"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:text="@string/customer_service"
                android:textColor="@color/text_secondary"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
