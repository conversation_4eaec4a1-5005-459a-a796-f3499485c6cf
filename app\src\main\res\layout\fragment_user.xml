<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background_white"
            android:elevation="4dp"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/devices"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- User Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="30dp">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/user_avatar"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:src="@drawable/ic_logo"
                    app:civ_border_color="@color/primary_blue"
                    app:civ_border_width="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="20dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/user_avatar"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="145306115"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/user_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/free_user"
                        android:textColor="@color/text_hint"
                        android:textSize="14sp" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Premium Promotion -->
        <androidx.cardview.widget.CardView
            android:id="@+id/premium_promotion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/premium_background"
                android:padding="20dp">

                <ImageView
                    android:id="@+id/premium_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_gem"
                    android:tint="@color/text_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/premium_content"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/view_plans_button"
                    app:layout_constraintStart_toEndOf="@id/premium_icon"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/join_premium"
                        android:textColor="@color/text_white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/access_all_servers"
                        android:textColor="@color/text_white"
                        android:textSize="14sp"
                        android:alpha="0.9" />

                </LinearLayout>

                <Button
                    android:id="@+id/view_plans_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/button_premium"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="@string/view_plans"
                    android:textColor="@color/text_white"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Settings Menu -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:orientation="vertical">

            <!-- Set Password -->
            <androidx.cardview.widget.CardView
                android:id="@+id/setting_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <View
                        android:id="@+id/password_icon_bg"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/setting_icon_background"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_settings"
                        android:tint="@color/primary_blue"
                        app:layout_constraintBottom_toBottomOf="@id/password_icon_bg"
                        app:layout_constraintEnd_toEndOf="@id/password_icon_bg"
                        app:layout_constraintStart_toStartOf="@id/password_icon_bg"
                        app:layout_constraintTop_toTopOf="@id/password_icon_bg" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:text="@string/set_password"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/password_arrow"
                        app:layout_constraintStart_toEndOf="@id/password_icon_bg"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/password_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- Bind Email -->
            <androidx.cardview.widget.CardView
                android:id="@+id/setting_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <View
                        android:id="@+id/email_icon_bg"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/setting_icon_background"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_email"
                        android:tint="@color/primary_blue"
                        app:layout_constraintBottom_toBottomOf="@id/email_icon_bg"
                        app:layout_constraintEnd_toEndOf="@id/email_icon_bg"
                        app:layout_constraintStart_toStartOf="@id/email_icon_bg"
                        app:layout_constraintTop_toTopOf="@id/email_icon_bg" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:text="@string/bind_email"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/email_arrow"
                        app:layout_constraintStart_toEndOf="@id/email_icon_bg"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/email_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- Login Devices -->
            <androidx.cardview.widget.CardView
                android:id="@+id/setting_devices"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <View
                        android:id="@+id/devices_icon_bg"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/setting_icon_background"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_devices"
                        android:tint="@color/primary_blue"
                        app:layout_constraintBottom_toBottomOf="@id/devices_icon_bg"
                        app:layout_constraintEnd_toEndOf="@id/devices_icon_bg"
                        app:layout_constraintStart_toStartOf="@id/devices_icon_bg"
                        app:layout_constraintTop_toTopOf="@id/devices_icon_bg" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:text="@string/login_devices"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/devices_arrow"
                        app:layout_constraintStart_toEndOf="@id/devices_icon_bg"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/devices_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

            <!-- Switch Account -->
            <androidx.cardview.widget.CardView
                android:id="@+id/setting_switch_account"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                android:elevation="4dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="20dp">

                    <View
                        android:id="@+id/switch_icon_bg"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/setting_icon_background"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_switch"
                        android:tint="@color/primary_blue"
                        app:layout_constraintBottom_toBottomOf="@id/switch_icon_bg"
                        app:layout_constraintEnd_toEndOf="@id/switch_icon_bg"
                        app:layout_constraintStart_toStartOf="@id/switch_icon_bg"
                        app:layout_constraintTop_toTopOf="@id/switch_icon_bg" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="15dp"
                        android:layout_marginEnd="15dp"
                        android:text="@string/switch_account"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@id/switch_arrow"
                        app:layout_constraintStart_toEndOf="@id/switch_icon_bg"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageView
                        android:id="@+id/switch_arrow"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@drawable/ic_arrow_right"
                        android:tint="@color/text_hint"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <!-- Logout Button -->
        <Button
            android:id="@+id/logout_button"
            style="@style/DangerButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/logout" />

    </LinearLayout>

</ScrollView>
