@echo off
echo 正在设置 星链 VPN Android 项目...
echo.

echo 1. 检查 Java 版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到 Java。请安装 JDK 11 或更高版本。
    pause
    exit /b 1
)

echo.
echo 2. 检查 Android SDK...
if not exist "%ANDROID_HOME%" (
    if not exist "%LOCALAPPDATA%\Android\Sdk" (
        echo 警告: 未找到 Android SDK。请确保已安装 Android Studio。
    ) else (
        set ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk
        echo 找到 Android SDK: %ANDROID_HOME%
    )
) else (
    echo 找到 Android SDK: %ANDROID_HOME%
)

echo.
echo 3. 创建 local.properties 文件...
if not exist "local.properties" (
    echo sdk.dir=%LOCALAPPDATA%\Android\Sdk > local.properties
    echo 已创建 local.properties 文件
) else (
    echo local.properties 文件已存在
)

echo.
echo 4. 项目设置完成！
echo.
echo 接下来的步骤：
echo 1. 在 Android Studio 中打开此项目
echo 2. 等待 Gradle 同步完成
echo 3. 点击 "Build" -> "Make Project" 编译项目
echo 4. 运行应用到设备或模拟器
echo.
echo 如果遇到问题，请查看 README.md 文件中的故障排除部分。
echo.
pause
