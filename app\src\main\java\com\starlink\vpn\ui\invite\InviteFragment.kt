package com.starlink.vpn.ui.invite

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.starlink.vpn.databinding.FragmentInviteBinding

class InviteFragment : Fragment() {

    private var _binding: FragmentInviteBinding? = null
    private val binding get() = _binding!!
    
    private val inviteLink = "https://starlink-vpn.com/invite/abc123"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentInviteBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupClickListeners()
        animateQRCode()
    }
    
    private fun setupClickListeners() {
        binding.copyLinkButton.setOnClickListener {
            copyInviteLink()
        }
    }
    
    private fun copyInviteLink() {
        val clipboard = requireContext().getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("邀请链接", inviteLink)
        clipboard.setPrimaryClip(clip)
        
        showToast("邀请链接已复制到剪贴板")
    }
    
    private fun animateQRCode() {
        // Add a simple pulse animation to the QR code
        binding.qrPattern.animate()
            .scaleX(1.05f)
            .scaleY(1.05f)
            .setDuration(1000)
            .withEndAction {
                binding.qrPattern.animate()
                    .scaleX(1.0f)
                    .scaleY(1.0f)
                    .setDuration(1000)
                    .withEndAction {
                        // Repeat animation
                        animateQRCode()
                    }
                    .start()
            }
            .start()
    }
    
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
