/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    overflow: hidden;
}

/* 应用容器 */
.app-container {
    display: flex;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 侧边栏样式 */
.sidebar {
    width: 80px;
    background: linear-gradient(180deg, #5a70f4 0%, #5a70f4 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
}

.sidebar-header {
    margin-bottom: 40px;
}

.sidebar .logo {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.sidebar-menu {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 18px;
}

.sidebar-menu .nav-link:hover,
.sidebar-menu .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateX(5px);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: #f8f9fa;
    position: relative;
    overflow: hidden;
}

/* 页面通用样式 */
.page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    overflow-y: auto;
    padding: 0;
}

.page.active {
    opacity: 1;
    transform: translateX(0);
}

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 30px;
    background: white;
    border-bottom: 1px solid #eee;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo-image {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(90, 112, 244, 0.2);
}

.countdown-container {
    display: flex;
    align-items: center;
    padding: 12px 0;
    width: 100%;
}

.countdown-container i {
    color: #333;
    font-size: 18px;
    margin-right: 10px;
}

.countdown-time {
    font-weight: 600;
    color: #333;
    font-size: 18px;
}

.countdown-time.urgent {
    color: #e74c3c;
    font-weight: 600;
}

.countdown-time.expired {
    color: #e74c3c;
    font-weight: 600;
    animation: blink 1s infinite;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.back-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #f0f0f0;
}

.page-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.upgrade-btn {
    background: linear-gradient(135deg, #5a70f4, #4c5fd6);
    color: white;
    border: none;
    padding: 10px 18px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.upgrade-btn i {
    font-size: 14px;
    color: #ffd700;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(90, 112, 244, 0.3);
}

/* 登录页面样式 */
#login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translateX(0);
    opacity: 1;
}

.login-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.app-logo {
    margin-bottom: 20px;
}

.logo-image {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    margin: 0 auto;
    display: block;
    box-shadow: 0 5px 15px rgba(90, 112, 244, 0.2);
}

.sidebar-logo-image {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: block;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.2);
}

.login-header h2 {
    color: #333;
    font-size: 20px;
    font-weight: 500;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 15px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.input-group:focus-within {
    border-color: #5a70f4;
    background: white;
}

.input-group i {
    color: #999;
    margin-right: 12px;
    font-size: 16px;
}

.input-group input {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    font-size: 16px;
    color: #333;
}

.input-group input::placeholder {
    color: #999;
}

.toggle-password {
    cursor: pointer;
    margin-left: 12px;
    margin-right: 0;
}

.forgot-password {
    text-align: center;
}

.forgot-password a {
    color: #5a70f4;
    text-decoration: none;
    font-size: 14px;
}

.login-btn {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(90, 112, 244, 0.3);
}

.login-footer {
    text-align: center;
    font-size: 14px;
    color: #666;
}

.anonymous-login-link {
    color: #5a70f4;
    text-decoration: none;
    margin-left: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.anonymous-login-link:hover {
    color: #5a70f4;
    text-decoration: underline;
}

/* 主页面 - 连接状态 */
#home-page {
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

.home-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
}

/* 会员状态 */
/* 用户会员卡片 */
.user-membership-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.user-profile-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.user-profile-left {
    display: flex;
    align-items: center;
}

.user-avatar-small {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.user-avatar-small img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.user-info-text {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.user-type {
    font-size: 14px;
    color: #666;
}

.user-profile-right {
    text-align: right;
    display: flex;
    align-items: center;
}

.expiry-date {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-right: 5px;
}

.expiry-date.expired {
    color: #ff3333;
    font-weight: 800;
    font-size: 22px;
}

.expiry-date.urgent {
    color: #ff3333;
    font-weight: 700;
    font-size: 20px;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.expiry-label {
    font-size: 12px;
    color: #666;
}

.countdown-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.countdown-time {
    font-size: 24px;
    font-weight: 700;
    color: #5a70f4;
    letter-spacing: 1px;
    position: relative;
    display: inline-block;
}

.countdown-time::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #5a70f4, transparent);
}

.countdown-time.warning {
    color: #e67e22;
    text-shadow: 0 0 5px rgba(230, 126, 34, 0.3);
}

.countdown-time.warning::after {
    background: linear-gradient(90deg, #e67e22, transparent);
}

.countdown-time.urgent {
    color: #e74c3c;
    font-weight: 800;
    text-shadow: 0 0 8px rgba(231, 76, 60, 0.4);
    animation: pulse 1.5s infinite;
}

.countdown-time.urgent::after {
    background: linear-gradient(90deg, #e74c3c, transparent);
    height: 3px;
}

.countdown-time.expired {
    color: #e74c3c;
    font-weight: 800;
    text-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
    animation: blink 1s infinite;
}

.countdown-time.expired::after {
    background: linear-gradient(90deg, #e74c3c, #e74c3c);
    height: 3px;
    animation: pulse-width 2s infinite;
}

@keyframes pulse-width {
    0% { width: 30%; }
    50% { width: 100%; }
    100% { width: 30%; }
}

/* 连接卡片 */
.connection-card {
    background: white;
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 20px;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.connection-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.connection-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.connection-info {
    display: flex;
    align-items: center;
}

.status-label {
    font-size: 16px;
    font-weight: 500;
    margin-right: 8px;
}

.connection-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e74c3c;
}

.connection-dot.connected {
    background: #2ecc71;
    box-shadow: 0 0 0 4px rgba(46, 204, 113, 0.2);
}

.connection-dot.disconnected {
    background: #e74c3c;
    box-shadow: 0 0 0 4px rgba(231, 76, 60, 0.2);
}

.connection-details {
    display: flex;
    margin-bottom: 24px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 20px;
}

.detail-item {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.detail-value {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.connection-actions {
    display: flex;
    gap: 15px;
}

.connect-btn {
    flex: 1;
    background: linear-gradient(135deg, #5a70f4, #4c5fd6);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.connect-btn i {
    font-size: 18px;
}

.connect-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(90, 112, 244, 0.3);
}

.server-select-btn {
    flex: 1;
    background: white;
    color: #5a70f4;
    border: 2px solid #5a70f4;
    padding: 14px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.server-select-btn i {
    font-size: 18px;
}

.server-select-btn:hover {
    background: rgba(90, 112, 244, 0.05);
    transform: translateY(-2px);
}

/* 连接统计 */
.connection-stats {
    display: flex;
    gap: 15px;
}

.stat-card {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.stat-card i {
    font-size: 24px;
    color: #5a70f4;
    margin-right: 15px;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 13px;
    color: #666;
}

/* 用户页面样式 */
.user-info {
    display: flex;
    align-items: center;
    padding: 30px;
    background: white;
    margin: 20px;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    background: white;
    padding: 5px;
    box-shadow: 0 3px 10px rgba(90, 112, 244, 0.2);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
}

.avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}

.user-details h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.user-details p {
    color: #999;
    font-size: 14px;
}

.premium-promotion {
    display: flex;
    align-items: center;
    padding: 20px 30px;
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    margin: 20px;
    border-radius: 16px;
    color: white;
}

.premium-icon {
    font-size: 40px;
    margin-right: 20px;
    opacity: 0.9;
}

.premium-content {
    flex: 1;
}

.premium-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.premium-content p {
    font-size: 14px;
    opacity: 0.9;
}

.view-plans-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-plans-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.settings-menu {
    margin: 20px;
}

.setting-item {
    display: flex;
    align-items: center;
    padding: 20px;
    background: white;
    margin-bottom: 10px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.setting-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.setting-item i:first-child {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #5a70f4;
    margin-right: 15px;
}

.setting-item span {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.setting-item i:last-child {
    color: #ccc;
}

.logout-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    width: calc(100% - 40px);
    margin: 20px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
}

/* 服务器页面样式 */
.server-section {
    margin: 20px;
}

.server-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-left: 10px;
}

.server-list {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.server-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.server-item:last-child {
    border-bottom: none;
}

.server-item:hover {
    background: #f8f9fa;
}

.server-item.active {
    background:#5a70f4;
}

.server-flag {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
}

.server-flag.china {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
}

.server-flag.usa {
    background: linear-gradient(135deg, #3498db, #2980b9);
}

.server-name {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.server-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ddd;
}

.server-status.connected {
    background: #2ecc71;
    animation: pulse 2s infinite;
}

/* 邀请页面样式 */
.invite-content {
    padding: 40px;
    text-align: center;
    position: relative;
}

.invite-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.decoration-blob {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    opacity: 0.1;
}

.blob1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: -50px;
}

.blob2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: -30px;
}

.qr-code {
    margin: 40px 0;
    display: flex;
    justify-content: center;
}

.qr-placeholder {
    width: 200px;
    height: 200px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-pattern {
    width: 160px;
    height: 160px;
    background: 
        repeating-linear-gradient(
            0deg,
            #333 0px,
            #333 4px,
            transparent 4px,
            transparent 8px
        ),
        repeating-linear-gradient(
            90deg,
            #333 0px,
            #333 4px,
            transparent 4px,
            transparent 8px
        );
    background-size: 8px 8px;
}

.invite-content h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.invite-content p {
    color: #666;
    font-size: 16px;
    margin-bottom: 5px;
}

.invite-note {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
}

.copy-link-btn {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(90, 112, 244, 0.3);
}

/* 订阅计划页面样式 */
.plans-illustration {
    text-align: center;
    padding: 40px;
}

.plans-icon {
    position: relative;
    display: inline-block;
}

.plans-icon i {
    font-size: 80px;
    color: #5a70f4;
    margin: 0 10px;
}

.plans-list {
    margin: 20px;
}

.plan-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: white;
    margin-bottom: 15px;
    border-radius: 16px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.plan-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.plan-item.recommended {
    border-color: #ffd700;
    background: linear-gradient(135deg, #fff9e6, #fff3cd);
}

.plan-item.selected {
    border-color: #5a70f4;
    background: linear-gradient(135deg, #f0f4ff, #e6ecff);
}

.plan-duration {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.plan-price {
    font-size: 14px;
    color: #666;
    text-align: center;
    flex: 1;
    margin: 0 20px;
}

.plan-badge {
    background: #ffd700;
    color: #333;
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
}

.plan-button {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.plan-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    background: white;
    margin: 20px;
    border-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.total-label {
    font-size: 18px;
    color: #333;
}

.total-price {
    font-size: 24px;
    font-weight: 700;
    color: #5a70f4;
}

.plan-note {
    padding: 0 30px;
    margin-bottom: 30px;
}

.plan-note p {
    font-size: 12px;
    color: #666;
    line-height: 1.5;
}

.continue-btn {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    width: calc(100% - 60px);
    margin: 0 30px 20px;
    transition: all 0.3s ease;
}

.continue-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(90, 112, 244, 0.3);
}

.plan-links {
    text-align: center;
    padding: 0 30px 30px;
}

.plan-links a {
    color: #666;
    text-decoration: none;
    font-size: 12px;
    margin: 0 5px;
}

.plan-links span {
    color: #ccc;
    margin: 0 5px;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: 20px;
    padding: 30px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    transform: scale(0.8);
    transition: all 0.3s ease;
    position: relative;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f0f0f0;
}

.modal-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.modal-icon.warning {
    background: #fff3cd;
    color: #856404;
}

.modal-icon.error {
    background: #f8d7da;
    color: #721c24;
}

.modal h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.modal p {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
    line-height: 1.5;
}

.modal-btn {
    background: linear-gradient(135deg, #5a70f4, #5a70f4);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(90, 112, 244, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .app-container {
        flex-direction: column;
        height: 100vh;
        border-radius: 0;
    }
    
    .sidebar {
        width: 100%;
        height: 60px;
        flex-direction: row;
        justify-content: space-around;
        padding: 10px 20px;
        order: 2;
    }
    
    .sidebar-header {
        display: none;
    }
    
    .sidebar-menu {
        flex-direction: row;
        gap: 0;
        width: 100%;
        justify-content: space-around;
    }
    
    .main-content {
        order: 1;
        flex: 1;
    }
    
    .page-header {
        padding: 15px 20px;
    }
    
    .connection-status {
        width: 250px;
        height: 250px;
    }
    
    .status-time {
        font-size: 28px !important;
    }
    
    .login-container {
        padding: 30px 20px;
    }
    
    .user-info,
    .premium-promotion,
    .settings-menu,
    .server-section,
    .plan-total {
        margin: 15px;
    }
    
    .invite-content,
    .plans-illustration {
        padding: 30px 20px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page.active .user-info,
.page.active .premium-promotion,
.page.active .setting-item,
.page.active .server-item,
.page.active .plan-item {
    animation: fadeInUp 0.6s ease forwards;
}

.page.active .setting-item:nth-child(2) { animation-delay: 0.1s; }
.page.active .setting-item:nth-child(3) { animation-delay: 0.2s; }
.page.active .setting-item:nth-child(4) { animation-delay: 0.3s; }

/* 滚动条样式 */
.page::-webkit-scrollbar {
    width: 6px;
}

.page::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.page::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}

.page::-webkit-scrollbar-thumb:hover {
    background: #999;
}
