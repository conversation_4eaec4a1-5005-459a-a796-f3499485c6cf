@echo off
echo Java 版本修复工具
echo ==================
echo.

echo 1. 检查当前 Java 版本...
java -version 2>&1 | findstr "version"
echo.

echo 2. 检查 JAVA_HOME 环境变量...
if defined JAVA_HOME (
    echo JAVA_HOME: %JAVA_HOME%
) else (
    echo JAVA_HOME 未设置
)
echo.

echo 3. 查找系统中的 Java 安装...
echo 正在搜索 Java 安装目录...

set JAVA8_PATH=
set JAVA11_PATH=

if exist "C:\Program Files\Java" (
    echo 在 C:\Program Files\Java 中找到：
    dir "C:\Program Files\Java" /b
    
    for /d %%i in ("C:\Program Files\Java\jdk1.8*") do (
        set JAVA8_PATH=%%i
        echo 找到 Java 8: %%i
    )
    
    for /d %%i in ("C:\Program Files\Java\jdk-11*") do (
        set JAVA11_PATH=%%i
        echo 找到 Java 11: %%i
    )
)

if exist "C:\Program Files\OpenJDK" (
    echo 在 C:\Program Files\OpenJDK 中找到：
    dir "C:\Program Files\OpenJDK" /b
    
    for /d %%i in ("C:\Program Files\OpenJDK\jdk-11*") do (
        set JAVA11_PATH=%%i
        echo 找到 OpenJDK 11: %%i
    )
)

echo.
echo 4. 修复建议...

if defined JAVA11_PATH (
    echo 建议使用 Java 11: %JAVA11_PATH%
    echo.
    echo 要修复此问题，请在 gradle.properties 文件中添加：
    echo org.gradle.java.home=%JAVA11_PATH%
    echo.
    echo 是否自动修复？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo org.gradle.java.home=%JAVA11_PATH% >> gradle.properties
        echo 已添加 Java 路径到 gradle.properties
    )
) else if defined JAVA8_PATH (
    echo 建议使用 Java 8: %JAVA8_PATH%
    echo.
    echo 要修复此问题，请在 gradle.properties 文件中添加：
    echo org.gradle.java.home=%JAVA8_PATH%
    echo.
    echo 是否自动修复？ (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        echo org.gradle.java.home=%JAVA8_PATH% >> gradle.properties
        echo 已添加 Java 路径到 gradle.properties
    )
) else (
    echo 未找到兼容的 Java 版本。
    echo 请安装 JDK 8 或 JDK 11。
    echo.
    echo 下载链接：
    echo JDK 11: https://adoptium.net/temurin/releases/
    echo JDK 8: https://adoptium.net/temurin/releases/?version=8
)

echo.
echo 5. 清理 Gradle 缓存...
if exist "%USERPROFILE%\.gradle\caches" (
    echo 正在清理 Gradle 缓存...
    rmdir /s /q "%USERPROFILE%\.gradle\caches" 2>nul
    echo Gradle 缓存已清理
)

echo.
echo 修复完成！现在可以尝试在 Android Studio 中打开项目。
echo.
pause
