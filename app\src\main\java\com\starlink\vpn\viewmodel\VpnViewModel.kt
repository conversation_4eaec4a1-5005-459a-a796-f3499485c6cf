package com.starlink.vpn.viewmodel

import android.app.Application
import android.content.Context
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import kotlinx.coroutines.*
import java.util.*

class VpnViewModel(application: Application) : AndroidViewModel(application) {
    
    private val sharedPrefs = application.getSharedPreferences("vpn_prefs", Context.MODE_PRIVATE)
    
    // Connection state
    private val _isConnected = MutableLiveData<Boolean>(false)
    val isConnected: LiveData<Boolean> = _isConnected
    
    private val _connectionTime = MutableLiveData<Long>(0L)
    val connectionTime: LiveData<Long> = _connectionTime
    
    private val _selectedServer = MutableLiveData<String>("智能连接")
    val selectedServer: LiveData<String> = _selectedServer
    
    // User state
    private val _userName = MutableLiveData<String>()
    val userName: LiveData<String> = _userName
    
    private val _userType = MutableLiveData<String>()
    val userType: LiveData<String> = _userType
    
    private val _membershipExpireDate = MutableLiveData<Date>()
    val membershipExpireDate: LiveData<Date> = _membershipExpireDate
    
    private val _membershipExpired = MutableLiveData<Boolean>(false)
    val membershipExpired: LiveData<Boolean> = _membershipExpired
    
    // Connection timer
    private var connectionTimer: Job? = null
    private var membershipTimer: Job? = null
    
    init {
        loadUserData()
        startMembershipCountdown()
    }
    
    fun isLoggedIn(): Boolean {
        return sharedPrefs.getBoolean("is_logged_in", false)
    }
    
    fun login(username: String, password: String, callback: (Boolean) -> Unit) {
        // Simulate login process
        CoroutineScope(Dispatchers.Main).launch {
            delay(1500) // Simulate network delay
            
            // Save login state
            sharedPrefs.edit()
                .putBoolean("is_logged_in", true)
                .putString("username", username)
                .putString("user_type", "高级用户")
                .apply()
            
            _userName.value = username
            _userType.value = "高级用户"
            
            callback(true)
        }
    }
    
    fun anonymousLogin(callback: (Boolean) -> Unit) {
        CoroutineScope(Dispatchers.Main).launch {
            delay(1000) // Simulate network delay
            
            val anonymousName = "匿名用户_${(1000..9999).random()}"
            
            // Save login state
            sharedPrefs.edit()
                .putBoolean("is_logged_in", true)
                .putString("username", anonymousName)
                .putString("user_type", "免费用户")
                .apply()
            
            _userName.value = anonymousName
            _userType.value = "免费用户"
            
            // Set membership expiry for free users (30 minutes)
            val expireDate = Calendar.getInstance().apply {
                add(Calendar.MINUTE, 30)
            }.time
            _membershipExpireDate.value = expireDate
            
            callback(true)
        }
    }
    
    fun logout() {
        // Stop connection if active
        if (_isConnected.value == true) {
            disconnect()
        }
        
        // Clear user data
        sharedPrefs.edit().clear().apply()
        
        _userName.value = ""
        _userType.value = ""
        _membershipExpireDate.value = null
        _membershipExpired.value = false
        
        // Cancel timers
        connectionTimer?.cancel()
        membershipTimer?.cancel()
    }
    
    fun connect() {
        if (_membershipExpired.value == true) {
            return // Cannot connect if membership expired
        }
        
        _isConnected.value = true
        _connectionTime.value = 0L
        startConnectionTimer()
    }
    
    fun disconnect() {
        _isConnected.value = false
        connectionTimer?.cancel()
    }
    
    fun selectServer(serverName: String) {
        if (_membershipExpired.value == true) {
            return // Cannot select server if membership expired
        }
        
        _selectedServer.value = serverName
        
        // If connected, simulate reconnection
        if (_isConnected.value == true) {
            CoroutineScope(Dispatchers.Main).launch {
                delay(1500) // Simulate reconnection delay
                // Connection maintained
            }
        }
    }
    
    private fun loadUserData() {
        _userName.value = sharedPrefs.getString("username", "")
        _userType.value = sharedPrefs.getString("user_type", "免费用户")
        
        // Set default membership expiry for free users
        if (_userType.value == "免费用户") {
            val expireDate = Calendar.getInstance().apply {
                add(Calendar.MINUTE, 30)
            }.time
            _membershipExpireDate.value = expireDate
        }
    }
    
    private fun startConnectionTimer() {
        connectionTimer?.cancel()
        connectionTimer = CoroutineScope(Dispatchers.Main).launch {
            while (_isConnected.value == true) {
                delay(1000)
                _connectionTime.value = (_connectionTime.value ?: 0L) + 1L
            }
        }
    }
    
    private fun startMembershipCountdown() {
        membershipTimer?.cancel()
        membershipTimer = CoroutineScope(Dispatchers.Main).launch {
            while (true) {
                delay(1000)
                checkMembershipExpiry()
            }
        }
    }
    
    private fun checkMembershipExpiry() {
        val expireDate = _membershipExpireDate.value
        if (expireDate != null && Date().after(expireDate)) {
            _membershipExpired.value = true
            
            // Auto disconnect if connected
            if (_isConnected.value == true) {
                disconnect()
            }
        }
    }
    
    fun formatConnectionTime(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60
        return String.format("%02d:%02d:%02d", hours, minutes, secs)
    }
    
    fun formatMembershipTime(expireDate: Date): String {
        val now = Date()
        val timeDiff = expireDate.time - now.time
        
        if (timeDiff <= 0) {
            return "已过期"
        }
        
        val minutesLeft = timeDiff / (1000 * 60)
        val secondsLeft = (timeDiff % (1000 * 60)) / 1000
        
        return if (minutesLeft <= 30) {
            "${minutesLeft}分${secondsLeft}秒"
        } else {
            val calendar = Calendar.getInstance().apply { time = expireDate }
            "${calendar.get(Calendar.YEAR)}年${calendar.get(Calendar.MONTH) + 1}月${calendar.get(Calendar.DAY_OF_MONTH)}日"
        }
    }
    
    override fun onCleared() {
        super.onCleared()
        connectionTimer?.cancel()
        membershipTimer?.cancel()
    }
}
