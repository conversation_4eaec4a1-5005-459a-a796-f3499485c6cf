// 应用状态管理
class VPNApp {
    constructor() {
        this.currentPage = 'home-page';
        this.isConnected = false;
        this.connectionTime = 0;
        this.connectionTimer = null;
        this.isLoggedIn = true; // 默认已登录状态（匿名登录）
        this.selectedServer = '智能连接';
        this.loginType = 'anonymous'; // 记录登录类型：'anonymous' 或 'account'
        this.userName = '*********'; // 用户名称
        this.userType = '免费用户'; // 用户类型
        
        // 设置免费用户默认30分钟到期
        this.membershipExpireDate = new Date();
        this.membershipExpireDate.setMinutes(this.membershipExpireDate.getMinutes() + 30);
        
        this.membershipCountdownTimer = null;
        this.membershipExpired = false;
        this.membershipExpiredShown = false;
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.showPage('home-page'); // 默认显示首页
        this.updateConnectionStatus();
        this.initMembershipCountdown();
        this.checkMembershipStatus();
        this.updateUserInfo(); // 更新用户信息
    }
    
    // 更新用户信息显示
    updateUserInfo() {
        // 更新首页的用户信息
        const homeUserName = document.querySelector('.user-profile-card .user-name');
        const homeUserType = document.querySelector('.user-profile-card .user-type');
        
        if (homeUserName) homeUserName.textContent = this.userName;
        if (homeUserType) homeUserType.textContent = this.userType;
        
        // 更新用户页面的用户信息
        const userPageName = document.querySelector('#user-page .user-details h3');
        const userPageType = document.querySelector('#user-page .user-details p');
        
        if (userPageName) userPageName.textContent = this.userName;
        if (userPageType) userPageType.textContent = this.userType;
    }
    
    // 初始化会员倒计时
    initMembershipCountdown() {
        // 清除之前的计时器
        if (this.membershipCountdownTimer) {
            clearInterval(this.membershipCountdownTimer);
        }
        
        // 设置倒计时更新
        this.membershipCountdownTimer = setInterval(() => {
            this.updateMembershipCountdown();
        }, 1000);
        
        // 立即更新一次
        this.updateMembershipCountdown();
    }
    
    // 更新会员倒计时显示
    updateMembershipCountdown() {
        const countdownElement = document.getElementById('countdown-timer');
        const expiryLabelElement = document.querySelector('.expiry-label');
        if (!countdownElement || !expiryLabelElement) return;
        
        const now = new Date();
        const timeDiff = this.membershipExpireDate - now;
        
        if (timeDiff <= 0) {
            // 会员已过期
            countdownElement.textContent = '已过期';
            countdownElement.classList.add('expired');
            this.membershipExpired = true;
            
            // 隐藏"到期"文案
            expiryLabelElement.style.display = 'none';
            
            // 如果当前已连接，自动断开连接
            if (this.isConnected) {
                this.isConnected = false;
                this.stopConnection();
                this.updateConnectionStatus();
                this.showToast('会员已到期，连接已自动断开');
            }
            
            // 如果在首页，显示过期弹窗
            if (this.currentPage === 'home-page' && !this.membershipExpiredShown) {
                this.showModal('membership-expired-modal');
                this.membershipExpiredShown = true;
            }
            
            return;
        }
        
        // 计算剩余分钟数
        const minutesLeft = Math.floor(timeDiff / (1000 * 60));
        const secondsLeft = Math.floor((timeDiff % (1000 * 60)) / 1000);
        
        // 如果剩余时间小于等于30分钟，显示动态倒计时
        if (minutesLeft <= 30) {
            // 显示分钟和秒数倒计时
            countdownElement.textContent = `${minutesLeft}分${secondsLeft}秒`;
            countdownElement.classList.add('urgent');
            
            // 显示"到期"文案
            expiryLabelElement.style.display = 'block';
        } else {
            // 格式化日期为 "YYYY年MM月DD日" 格式
            const year = this.membershipExpireDate.getFullYear();
            const month = this.membershipExpireDate.getMonth() + 1; // getMonth() 返回 0-11
            const day = this.membershipExpireDate.getDate();
            
            // 更新显示
            countdownElement.textContent = `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
            
            // 显示"到期"文案
            expiryLabelElement.style.display = 'block';
            
            // 移除紧急样式
            countdownElement.classList.remove('urgent');
        }
        
        // 移除过期样式
        countdownElement.classList.remove('expired');
    }
    
    // 检查会员状态
    checkMembershipStatus() {
        // 如果会员已过期且在首页，显示过期弹窗
        if (this.membershipExpired && this.currentPage === 'home-page' && !this.membershipExpiredShown) {
            this.showModal('membership-expired-modal');
            this.membershipExpiredShown = true;
        }
    }

    // 绑定事件监听器
    bindEvents() {
        // 导航菜单点击事件
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.currentTarget.dataset.page;
                if (this.isLoggedIn) {
                    this.showPage(page + '-page');
                    this.updateActiveNav(e.currentTarget);
                } else {
                    this.showModal('not-logged-modal');
                }
            });
        });

        // 登录表单提交
        const loginForm = document.querySelector('.login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // 匿名登录
        const anonymousLoginLink = document.querySelector('.anonymous-login-link');
        if (anonymousLoginLink) {
            anonymousLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleAnonymousLogin();
            });
        }

        // 密码显示/隐藏切换
        const togglePassword = document.querySelector('.toggle-password');
        if (togglePassword) {
            togglePassword.addEventListener('click', this.togglePasswordVisibility);
        }

        // 连接按钮点击事件
        const connectBtn = document.getElementById('connect-btn');
        if (connectBtn) {
            connectBtn.addEventListener('click', () => this.toggleConnection());
        }
        
        // 服务器选择按钮
        const serverSelectBtn = document.querySelector('.server-select-btn');
        if (serverSelectBtn) {
            serverSelectBtn.addEventListener('click', () => {
                if (this.isLoggedIn) {
                    this.showPage('servers-page');
                    this.updateActiveNav(document.querySelector('[data-page="servers"]'));
                } else {
                    this.showModal('not-logged-modal');
                }
            });
        }

        // 服务器选择
        document.querySelectorAll('.server-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.selectServer(e.currentTarget);
            });
        });

        // 返回按钮
        document.querySelectorAll('.back-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.showPage('home-page');
                this.updateActiveNav(document.querySelector('[data-page="home"]'));
            });
        });

        // 升级按钮
        document.querySelectorAll('.upgrade-btn, .view-plans-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.showPage('plans-page');
            });
        });

        // 退出登录
        const logoutBtn = document.querySelector('.logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => {
                this.showModal('disconnect-modal');
            });
        }

        // 复制链接按钮
        const copyLinkBtn = document.querySelector('.copy-link-btn');
        if (copyLinkBtn) {
            copyLinkBtn.addEventListener('click', this.copyInviteLink);
        }

        // 订阅计划选择
        document.querySelectorAll('.plan-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.selectPlan(e.currentTarget);
            });
        });

        // 继续按钮
        const continueBtn = document.querySelector('.continue-btn');
        if (continueBtn) {
            continueBtn.addEventListener('click', this.handlePayment);
        }

        // 模态框事件
        this.bindModalEvents();

        // 设置项点击事件
        document.querySelectorAll('.setting-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.handleSettingClick(e.currentTarget);
            });
        });
    }

    // 绑定模态框事件
    bindModalEvents() {
        const modalOverlay = document.getElementById('modal-overlay');
        const modalCloses = document.querySelectorAll('.modal-close');
        const modalBtns = document.querySelectorAll('.modal-btn');

        // 点击遮罩层关闭模态框
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.hideModal();
                }
            });
        }

        // 关闭按钮
        modalCloses.forEach(btn => {
            btn.addEventListener('click', () => this.hideModal());
        });

        // 模态框按钮
        modalBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                if (modal.id === 'disconnect-modal') {
                    this.handleLogout();
                } else if (modal.id === 'membership-expired-modal' && e.target.classList.contains('upgrade-now-btn')) {
                    this.hideModal();
                    this.showPage('plans-page');
                }
                this.hideModal();
            });
        });
    }

    // 显示页面
    showPage(pageId) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(pageId);
        if (targetPage) {
            targetPage.classList.add('active');
            this.currentPage = pageId;
            
            // 如果切换到首页，检查会员状态
            if (pageId === 'home-page') {
                this.checkMembershipStatus();
            }
        }

        // 如果是登录页面，隐藏侧边栏；否则显示侧边栏
        const sidebar = document.querySelector('.sidebar');
        if (pageId === 'login-page') {
            sidebar.style.display = 'none';
        } else {
            sidebar.style.display = 'flex';
        }
    }

    // 更新导航激活状态
    updateActiveNav(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    // 处理登录
    handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showToast('请输入用户名和密码');
            return;
        }

        // 模拟登录过程
        this.showToast('登录中...');
        
        setTimeout(() => {
            this.isLoggedIn = true;
            this.loginType = 'account'; // 账户登录
            this.userName = username; // 设置用户名
            this.userType = '高级用户'; // 设置用户类型
            this.updateUserInfo(); // 更新用户信息显示
            this.showPage('home-page');
            this.updateActiveNav(document.querySelector('[data-page="home"]'));
            this.showToast('登录成功');
        }, 1500);
    }

    // 处理匿名登录
    handleAnonymousLogin() {
        this.showToast('匿名登录中...');
        
        setTimeout(() => {
            this.isLoggedIn = true;
            this.loginType = 'anonymous'; // 匿名登录
            this.userName = '匿名用户_' + Math.floor(Math.random() * 10000); // 生成随机匿名用户名
            this.userType = '免费用户'; // 设置用户类型
            this.updateUserInfo(); // 更新用户信息显示
            this.showPage('home-page');
            this.updateActiveNav(document.querySelector('[data-page="home"]'));
            this.showToast('匿名登录成功');
        }, 1000);
    }

    // 处理退出登录
    handleLogout() {
        this.isLoggedIn = false;
        this.isConnected = false;
        this.connectionTime = 0;
        this.loginType = null;
        this.clearConnectionTimer();
        this.updateConnectionStatus();
        this.showPage('login-page');
        this.showToast('已退出登录');
        
        // 清空登录表单
        const usernameInput = document.getElementById('username');
        const passwordInput = document.getElementById('password');
        if (usernameInput) usernameInput.value = '';
        if (passwordInput) passwordInput.value = '';
    }

    // 切换密码可见性
    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('.toggle-password');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        }
    }

    // 切换连接状态
    toggleConnection() {
        if (!this.isLoggedIn) {
            this.showModal('not-logged-modal');
            return;
        }
        
        // 检查会员是否已过期
        if (this.membershipExpired) {
            // 如果会员已过期，显示升级提示
            this.showModal('membership-expired-modal');
            return;
        }

        this.isConnected = !this.isConnected;
        
        if (this.isConnected) {
            this.startConnection();
        } else {
            this.stopConnection();
        }
        
        this.updateConnectionStatus();
    }

    // 开始连接
    startConnection() {
        this.connectionTime = 0;
        this.startConnectionTimer();
        this.showToast(`正在连接到 ${this.selectedServer}...`);
        
        // 模拟连接过程
        setTimeout(() => {
            this.showToast(`已连接到 ${this.selectedServer}`);
        }, 2000);
    }

    // 停止连接
    stopConnection() {
        this.clearConnectionTimer();
        this.showToast('连接已断开');
    }

    // 开始连接计时器
    startConnectionTimer() {
        this.connectionTimer = setInterval(() => {
            this.connectionTime++;
            this.updateConnectionTime();
        }, 1000);
    }

    // 清除连接计时器
    clearConnectionTimer() {
        if (this.connectionTimer) {
            clearInterval(this.connectionTimer);
            this.connectionTimer = null;
        }
    }

    // 更新连接时间显示
    updateConnectionTime() {
        const timeElement = document.getElementById('connection-time');
        if (timeElement) {
            const hours = Math.floor(this.connectionTime / 3600);
            const minutes = Math.floor((this.connectionTime % 3600) / 60);
            const seconds = this.connectionTime % 60;
            
            timeElement.textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // 更新连接状态显示
    updateConnectionStatus() {
        const statusLabel = document.getElementById('status-label');
        const connectionDot = document.querySelector('.connection-dot');
        const connectBtn = document.getElementById('connect-btn');

        if (!statusLabel || !connectionDot || !connectBtn) return;

        if (this.isConnected) {
            connectionDot.classList.remove('disconnected');
            connectionDot.classList.add('connected');
            statusLabel.textContent = '已连接';
            connectBtn.innerHTML = '<i class="fas fa-power-off"></i><span>断开</span>';
        } else {
            connectionDot.classList.remove('connected');
            connectionDot.classList.add('disconnected');
            statusLabel.textContent = '未连接';
            connectBtn.innerHTML = '<i class="fas fa-power-off"></i><span>连接</span>';
            this.updateConnectionTime();
        }
    }

    // 选择服务器
    selectServer(serverElement) {
        // 检查会员是否已过期
        if (this.membershipExpired) {
            // 如果会员已过期，显示升级提示
            this.showModal('membership-expired-modal');
            return;
        }
        
        // 移除所有活动状态
        document.querySelectorAll('.server-item').forEach(item => {
            item.classList.remove('active');
        });

        // 添加活动状态到选中的服务器
        serverElement.classList.add('active');

        // 更新选中的服务器
        const serverName = serverElement.querySelector('.server-name').textContent;
        this.selectedServer = serverName;

        // 如果当前已连接，重新连接到新服务器
        if (this.isConnected) {
            this.showToast(`正在切换到 ${serverName}...`);
            setTimeout(() => {
                this.showToast(`已连接到 ${serverName}`);
            }, 1500);
        }

        // 更新服务器状态指示器
        document.querySelectorAll('.server-status').forEach(status => {
            status.classList.remove('connected');
        });
        
        const selectedStatus = serverElement.querySelector('.server-status');
        if (selectedStatus && this.isConnected) {
            selectedStatus.classList.add('connected');
        }
    }

    // 选择订阅计划
    selectPlan(planElement) {
        // 移除所有选中状态
        document.querySelectorAll('.plan-item').forEach(item => {
            item.classList.remove('selected');
        });

        // 添加选中状态
        planElement.classList.add('selected');

        // 更新价格（这里可以根据实际需要实现）
        this.showToast('计划已选择');
    }

    // 处理支付
    handlePayment() {
        this.showToast('正在跳转到支付页面...');
        
        // 模拟支付流程
        setTimeout(() => {
            this.showToast('支付功能暂未开放');
        }, 1500);
    }

    // 复制邀请链接
    copyInviteLink() {
        const inviteLink = 'https://pioneer-vpn.com/invite/abc123';
        
        // 尝试使用现代 API
        if (navigator.clipboard) {
            navigator.clipboard.writeText(inviteLink).then(() => {
                this.showToast('邀请链接已复制到剪贴板');
            }).catch(() => {
                this.fallbackCopyTextToClipboard(inviteLink);
            });
        } else {
            this.fallbackCopyTextToClipboard(inviteLink);
        }
    }

    // 备用复制方法
    fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showToast('邀请链接已复制到剪贴板');
        } catch (err) {
            this.showToast('复制失败，请手动复制');
        }
        
        document.body.removeChild(textArea);
    }

    // 处理设置项点击
    handleSettingClick(settingElement) {
        const settingText = settingElement.querySelector('span').textContent;
        
        switch (settingText) {
            case '设置密码':
                this.showToast('密码设置功能开发中');
                break;
            case '绑定邮箱':
                this.showToast('邮箱绑定功能开发中');
                break;
            case '登录设备':
                this.showToast('设备管理功能开发中');
                break;
            case '切换账户':
                this.showModal('disconnect-modal');
                break;
            default:
                this.showToast('功能开发中');
        }
    }

    // 显示模态框
    showModal(modalId) {
        const modalOverlay = document.getElementById('modal-overlay');
        const modals = document.querySelectorAll('.modal');
        
        // 隐藏所有模态框
        modals.forEach(modal => {
            modal.style.display = 'none';
        });
        
        // 显示目标模态框
        const targetModal = document.getElementById(modalId);
        if (targetModal) {
            targetModal.style.display = 'block';
            modalOverlay.classList.add('active');
        }
    }

    // 隐藏模态框
    hideModal() {
        const modalOverlay = document.getElementById('modal-overlay');
        modalOverlay.classList.remove('active');
        
        setTimeout(() => {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                modal.style.display = 'none';
            });
        }, 300);
    }

    // 显示提示消息
    showToast(message, duration = 3000) {
        // 移除现有的 toast
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        // 创建新的 toast
        const toast = document.createElement('div');
        toast.className = 'toast';
        toast.textContent = message;
        
        // 添加 toast 样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '12px 24px',
            borderRadius: '25px',
            fontSize: '14px',
            zIndex: '9999',
            opacity: '0',
            transition: 'opacity 0.3s ease'
        });

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
        }, 100);

        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    // 生成二维码（简化版）
    generateQRCode() {
        // 这里可以集成真实的二维码生成库
        // 现在只是显示一个占位符
        const qrPattern = document.querySelector('.qr-pattern');
        if (qrPattern) {
            // 添加一些动画效果
            qrPattern.style.animation = 'pulse 2s infinite';
        }
    }

    // 模拟网络延迟
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 检查网络状态
    checkNetworkStatus() {
        if (navigator.onLine) {
            return true;
        } else {
            this.showToast('网络连接已断开');
            return false;
        }
    }

    // 保存用户设置到本地存储
    saveSettings() {
        const settings = {
            selectedServer: this.selectedServer,
            isLoggedIn: this.isLoggedIn,
            lastConnectionTime: this.connectionTime
        };
        localStorage.setItem('vpn-settings', JSON.stringify(settings));
    }

    // 从本地存储加载用户设置
    loadSettings() {
        const settings = localStorage.getItem('vpn-settings');
        if (settings) {
            const parsed = JSON.parse(settings);
            this.selectedServer = parsed.selectedServer || '智能连接';
            // 注意：出于安全考虑，不保存登录状态
        }
    }

    // 应用销毁时的清理工作
    destroy() {
        this.clearConnectionTimer();
        this.saveSettings();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    const app = new VPNApp();
    
    // 监听页面卸载事件，进行清理
    window.addEventListener('beforeunload', () => {
        app.destroy();
    });

    // 监听网络状态变化
    window.addEventListener('online', () => {
        app.showToast('网络连接已恢复');
    });

    window.addEventListener('offline', () => {
        app.showToast('网络连接已断开');
    });
    
    // 演示功能：快速倒计时结束（设置为10秒后到期）
    setTimeout(() => {
        // 设置会员到期时间为10秒后
        app.membershipExpireDate = new Date();
        app.membershipExpireDate.setSeconds(app.membershipExpireDate.getSeconds() + 10);
        app.membershipExpiredShown = false; // 重置弹窗显示状态
        app.showToast('演示：会员将在10秒后到期');
    }, 3000); // 页面加载3秒后开始演示

    // 键盘快捷键支持
    document.addEventListener('keydown', (e) => {
        // ESC 键关闭模态框
        if (e.key === 'Escape') {
            app.hideModal();
        }
        
        // Ctrl/Cmd + Enter 快速连接
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (app.currentPage === 'home-page') {
                app.toggleConnection();
            }
        }
    });

    // 触摸设备支持
    let touchStartY = 0;
    let touchEndY = 0;

    document.addEventListener('touchstart', (e) => {
        touchStartY = e.changedTouches[0].screenY;
    });

    document.addEventListener('touchend', (e) => {
        touchEndY = e.changedTouches[0].screenY;
        handleSwipe();
    });

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartY - touchEndY;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0) {
                // 向上滑动
                console.log('Swipe up detected');
            } else {
                // 向下滑动
                console.log('Swipe down detected');
            }
        }
    }

    // 开发者模式检测（可选）
    let devtools = false;
    setInterval(() => {
        if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
            if (!devtools) {
                devtools = true;
                console.log('Developer tools detected');
            }
        } else {
            devtools = false;
        }
    }, 500);

    // 性能监控（可选）
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
            }, 0);
        });
    }
});

// 全局错误处理
window.addEventListener('error', (e) => {
    console.error('Global error:', e.error);
    // 可以在这里添加错误上报逻辑
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
    // 可以在这里添加错误上报逻辑
});
