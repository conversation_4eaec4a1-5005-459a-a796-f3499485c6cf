# 星链 VPN Android 项目

这是一个基于原始 HTML/CSS/JavaScript 设计的 Android VPN 应用程序。

## 项目结构

```
app/
├── src/main/
│   ├── java/com/starlink/vpn/
│   │   ├── MainActivity.kt                 # 主活动
│   │   ├── ui/
│   │   │   ├── home/HomeFragment.kt       # 主页面
│   │   │   ├── user/UserFragment.kt       # 用户设置页面
│   │   │   ├── servers/ServersFragment.kt # 服务器选择页面
│   │   │   ├── invite/InviteFragment.kt   # 邀请好友页面
│   │   │   ├── login/LoginActivity.kt     # 登录页面
│   │   │   └── plans/PlansActivity.kt     # 订阅计划页面
│   │   └── viewmodel/VpnViewModel.kt      # 视图模型
│   ├── res/
│   │   ├── layout/                        # 布局文件
│   │   ├── drawable/                      # 图标和背景资源
│   │   ├── values/                        # 字符串、颜色、主题
│   │   ├── anim/                          # 动画资源
│   │   └── menu/                          # 菜单资源
│   └── AndroidManifest.xml
└── build.gradle

```

## 主要功能

1. **登录页面** - 用户认证界面
2. **主页面** - 连接状态和用户信息显示
3. **用户设置** - 个人资料和应用设置
4. **服务器选择** - VPN 服务器列表和选择
5. **邀请好友** - 二维码和邀请链接分享
6. **订阅计划** - 付费计划选择和购买

## 技术特性

- **MVVM 架构** - 使用 ViewModel 和 LiveData
- **Material Design 3** - 现代化 UI 设计
- **Fragment 导航** - 底部导航栏页面切换
- **ViewBinding** - 类型安全的视图引用
- **动画效果** - 页面切换和交互动画
- **响应式布局** - 适配不同屏幕尺寸

## 编译要求

- Android Studio Arctic Fox 或更高版本
- JDK 11 或更高版本
- Android SDK API 32
- Gradle 7.4

## 编译步骤

1. 在 Android Studio 中打开项目
2. 等待 Gradle 同步完成
3. 点击 "Build" -> "Make Project" 编译项目
4. 运行应用到设备或模拟器

## 故障排除

如果遇到编译错误：

1. **Java 版本问题**: 确保使用 JDK 11 或更高版本
2. **Gradle 版本问题**: 检查 gradle-wrapper.properties 中的 Gradle 版本
3. **SDK 版本问题**: 确保安装了 Android SDK API 32
4. **依赖问题**: 运行 "File" -> "Sync Project with Gradle Files"

## 注意事项

- 这是一个 UI 演示项目，不包含实际的 VPN 功能
- 需要集成真实的 VPN SDK 和支付系统才能正常工作
- 建议在真实设备上测试以获得最佳体验
