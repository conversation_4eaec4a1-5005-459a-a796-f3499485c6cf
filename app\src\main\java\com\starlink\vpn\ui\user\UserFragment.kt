package com.starlink.vpn.ui.user

import android.app.AlertDialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.starlink.vpn.MainActivity
import com.starlink.vpn.R
import com.starlink.vpn.databinding.FragmentUserBinding
import com.starlink.vpn.viewmodel.VpnViewModel

class UserFragment : Fragment() {

    private var _binding: FragmentUserBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var vpnViewModel: VpnViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentUserBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        vpnViewModel = ViewModelProvider(requireActivity())[VpnViewModel::class.java]
        
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupObservers() {
        vpnViewModel.userName.observe(viewLifecycleOwner) { name ->
            binding.userName.text = name
        }
        
        vpnViewModel.userType.observe(viewLifecycleOwner) { type ->
            binding.userType.text = type
        }
    }
    
    private fun setupClickListeners() {
        // Premium promotion
        binding.premiumPromotion.setOnClickListener {
            (activity as? MainActivity)?.navigateToPlans()
        }
        
        binding.viewPlansButton.setOnClickListener {
            (activity as? MainActivity)?.navigateToPlans()
        }
        
        // Settings
        binding.settingPassword.setOnClickListener {
            showToast("密码设置功能开发中")
        }
        
        binding.settingEmail.setOnClickListener {
            showToast("邮箱绑定功能开发中")
        }
        
        binding.settingDevices.setOnClickListener {
            showToast("设备管理功能开发中")
        }
        
        binding.settingSwitchAccount.setOnClickListener {
            showSwitchAccountDialog()
        }
        
        // Logout
        binding.logoutButton.setOnClickListener {
            showLogoutDialog()
        }
    }
    
    private fun showSwitchAccountDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.connection_will_interrupt))
            .setMessage(getString(R.string.switch_account_warning))
            .setPositiveButton(getString(R.string.ok)) { _, _ ->
                performLogout()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun showLogoutDialog() {
        AlertDialog.Builder(requireContext())
            .setTitle(getString(R.string.logout))
            .setMessage("确定要退出登录吗？")
            .setPositiveButton(getString(R.string.ok)) { _, _ ->
                performLogout()
            }
            .setNegativeButton(getString(R.string.cancel), null)
            .show()
    }
    
    private fun performLogout() {
        (activity as? MainActivity)?.logout()
    }
    
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
