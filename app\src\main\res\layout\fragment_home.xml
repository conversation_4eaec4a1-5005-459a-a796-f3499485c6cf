<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- User Membership Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/user_membership_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp">

                <!-- User Avatar -->
                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/user_avatar"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:src="@drawable/ic_logo"
                    app:civ_border_color="@color/primary_blue"
                    app:civ_border_width="2dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- User Info -->
                <LinearLayout
                    android:id="@+id/user_info"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="15dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/expiry_info"
                    app:layout_constraintStart_toEndOf="@id/user_avatar"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="用户昵称"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/user_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="会员类型"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

                <!-- Expiry Info -->
                <LinearLayout
                    android:id="@+id/expiry_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/expiry_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="4dp"
                        android:text="2025年09月01日"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/expiry_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/expires_on"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Connection Status Card -->
        <androidx.cardview.widget.CardView
            android:id="@+id/connection_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp">

                <!-- Connection Header -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/connection_status"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/status_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:text="@string/disconnected"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <View
                            android:id="@+id/connection_dot"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/connection_dot_disconnected" />

                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!-- Connection Details -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    android:orientation="horizontal"
                    android:paddingBottom="20dp"
                    android:background="@drawable/bottom_border">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="6dp"
                            android:text="@string/connection_time"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/connection_time"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="00:00:00"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="6dp"
                            android:text="@string/current_server"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/current_server"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/smart_connect"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <!-- Connection Actions -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="2">

                    <Button
                        android:id="@+id/connect_button"
                        style="@style/PrimaryButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_power"
                        android:drawablePadding="8dp"
                        android:text="@string/connect" />

                    <Button
                        android:id="@+id/server_select_button"
                        style="@style/SecondaryButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_server"
                        android:drawablePadding="8dp"
                        android:text="@string/select_server" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Connection Stats -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:weightSum="2">

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:elevation="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/ic_shield"
                        android:tint="@color/primary_blue" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/protected"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/network_security"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:layout_weight="1"
                android:elevation="8dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="15dp"
                        android:src="@drawable/ic_speed"
                        android:tint="@color/primary_blue" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="4dp"
                            android:text="@string/high_speed"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/optimized_speed"
                            android:textColor="@color/text_secondary"
                            android:textSize="13sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
