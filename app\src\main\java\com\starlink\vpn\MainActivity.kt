package com.starlink.vpn

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.starlink.vpn.databinding.ActivityMainBinding
import com.starlink.vpn.ui.home.HomeFragment
import com.starlink.vpn.ui.invite.InviteFragment
import com.starlink.vpn.ui.login.LoginActivity
import com.starlink.vpn.ui.servers.ServersFragment
import com.starlink.vpn.ui.user.UserFragment
import com.starlink.vpn.ui.plans.PlansActivity
import com.starlink.vpn.viewmodel.VpnViewModel

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var vpnViewModel: VpnViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        vpnViewModel = ViewModelProvider(this)[VpnViewModel::class.java]
        
        // Check if user is logged in
        if (!vpnViewModel.isLoggedIn()) {
            startLoginActivity()
            return
        }
        
        setupBottomNavigation()
        
        // Show home fragment by default
        if (savedInstanceState == null) {
            showFragment(HomeFragment())
            binding.bottomNavigation.selectedItemId = R.id.nav_home
        }
    }
    
    private fun startLoginActivity() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_home -> {
                    showFragment(HomeFragment())
                    true
                }
                R.id.nav_user -> {
                    showFragment(UserFragment())
                    true
                }
                R.id.nav_servers -> {
                    showFragment(ServersFragment())
                    true
                }
                R.id.nav_invite -> {
                    showFragment(InviteFragment())
                    true
                }
                else -> false
            }
        }
    }
    
    private fun showFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .setCustomAnimations(
                R.anim.slide_in_right,
                R.anim.slide_out_left,
                R.anim.slide_in_left,
                R.anim.slide_out_right
            )
            .replace(R.id.fragment_container, fragment)
            .commit()
    }
    
    fun navigateToPlans() {
        val intent = Intent(this, PlansActivity::class.java)
        startActivity(intent)
    }
    
    fun logout() {
        vpnViewModel.logout()
        startLoginActivity()
    }
}
