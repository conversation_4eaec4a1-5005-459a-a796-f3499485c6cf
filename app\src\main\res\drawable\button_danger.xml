<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_danger_pressed"
                android:endColor="@color/button_danger_pressed"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_danger"
                android:endColor="@color/button_danger"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
