package com.starlink.vpn.ui.servers

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.starlink.vpn.MainActivity
import com.starlink.vpn.R
import com.starlink.vpn.databinding.FragmentServersBinding
import com.starlink.vpn.viewmodel.VpnViewModel

class ServersFragment : Fragment() {

    private var _binding: FragmentServersBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var vpnViewModel: VpnViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentServersBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        vpnViewModel = ViewModelProvider(requireActivity())[VpnViewModel::class.java]
        
        setupObservers()
        setupClickListeners()
    }
    
    private fun setupObservers() {
        vpnViewModel.selectedServer.observe(viewLifecycleOwner) { server ->
            updateServerSelection(server)
        }
        
        vpnViewModel.isConnected.observe(viewLifecycleOwner) { connected ->
            updateConnectionStatus(connected)
        }
        
        vpnViewModel.membershipExpired.observe(viewLifecycleOwner) { expired ->
            if (expired) {
                // Disable server selection for expired users
                disableServerSelection()
            }
        }
    }
    
    private fun setupClickListeners() {
        // Premium promotion
        binding.premiumPromotion.setOnClickListener {
            (activity as? MainActivity)?.navigateToPlans()
        }
        
        binding.viewPlansButton.setOnClickListener {
            (activity as? MainActivity)?.navigateToPlans()
        }
        
        // Server selection
        binding.serverSmart.setOnClickListener {
            selectServer("智能连接")
        }
        
        binding.serverChinaHk.setOnClickListener {
            selectServer("中国香港")
        }
        
        binding.serverUsa.setOnClickListener {
            selectServer("美国")
        }
    }
    
    private fun selectServer(serverName: String) {
        if (vpnViewModel.membershipExpired.value == true) {
            showToast("会员已到期，请升级到高级版")
            return
        }
        
        vpnViewModel.selectServer(serverName)
        showToast("已选择服务器: $serverName")
    }
    
    private fun updateServerSelection(selectedServer: String) {
        // Reset all server backgrounds
        binding.serverSmart.setBackgroundResource(R.drawable.card_background)
        binding.serverChinaHk.setBackgroundResource(R.drawable.card_background)
        binding.serverUsa.setBackgroundResource(R.drawable.card_background)
        
        // Highlight selected server
        when (selectedServer) {
            "智能连接" -> binding.serverSmart.setBackgroundResource(R.drawable.server_item_selected)
            "中国香港" -> binding.serverChinaHk.setBackgroundResource(R.drawable.server_item_selected)
            "美国" -> binding.serverUsa.setBackgroundResource(R.drawable.server_item_selected)
        }
    }
    
    private fun updateConnectionStatus(connected: Boolean) {
        // Update status indicators based on connection and selected server
        val selectedServer = vpnViewModel.selectedServer.value ?: "智能连接"
        
        // Reset all status dots
        binding.smartStatus.setBackgroundResource(R.drawable.connection_dot_disconnected)
        binding.chinaStatus.setBackgroundResource(R.drawable.connection_dot_disconnected)
        binding.usaStatus.setBackgroundResource(R.drawable.connection_dot_disconnected)
        
        // Set connected status for selected server
        if (connected) {
            when (selectedServer) {
                "智能连接" -> binding.smartStatus.setBackgroundResource(R.drawable.connection_dot_connected)
                "中国香港" -> binding.chinaStatus.setBackgroundResource(R.drawable.connection_dot_connected)
                "美国" -> binding.usaStatus.setBackgroundResource(R.drawable.connection_dot_connected)
            }
        }
    }
    
    private fun disableServerSelection() {
        binding.serverSmart.isEnabled = false
        binding.serverChinaHk.isEnabled = false
        binding.serverUsa.isEnabled = false
        
        binding.serverSmart.alpha = 0.5f
        binding.serverChinaHk.alpha = 0.5f
        binding.serverUsa.alpha = 0.5f
    }
    
    private fun showToast(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_SHORT).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
