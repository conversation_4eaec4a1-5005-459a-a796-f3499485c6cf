<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    tools:context=".ui.login.LoginActivity">

    <androidx.cardview.widget.CardView
        android:id="@+id/login_card"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:elevation="20dp"
        app:cardCornerRadius="20dp"
        app:cardElevation="20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="40dp">

            <!-- Logo and Title -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:layout_marginBottom="30dp">

                <ImageView
                    android:id="@+id/app_logo"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/logo_background"
                    android:elevation="5dp"
                    android:src="@drawable/ic_logo"
                    android:contentDescription="@string/app_name" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/login_title"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="normal" />

            </LinearLayout>

            <!-- Username Input -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/input_background"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/username_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_user"
                    android:tint="@color/text_hint"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/username_hint" />

                <EditText
                    android:id="@+id/username_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:background="@null"
                    android:hint="@string/username_hint"
                    android:inputType="text"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/username_icon"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Password Input -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:background="@drawable/input_background"
                android:padding="15dp">

                <ImageView
                    android:id="@+id/password_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_lock"
                    android:tint="@color/text_hint"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="@string/password_hint" />

                <EditText
                    android:id="@+id/password_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:background="@null"
                    android:hint="@string/password_hint"
                    android:inputType="textPassword"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/password_toggle"
                    app:layout_constraintStart_toEndOf="@id/password_icon"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/password_toggle"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_eye_off"
                    android:tint="@color/text_hint"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:contentDescription="Toggle password visibility" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Forgot Password -->
            <TextView
                android:id="@+id/forgot_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginBottom="20dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground"
                android:padding="8dp"
                android:text="@string/forgot_password"
                android:textColor="@color/primary_blue"
                android:textSize="14sp" />

            <!-- Login Button -->
            <Button
                android:id="@+id/login_button"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="20dp"
                android:text="@string/login_button" />

            <!-- Anonymous Login -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_account"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/anonymous_login"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:text="@string/anonymous_login"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
