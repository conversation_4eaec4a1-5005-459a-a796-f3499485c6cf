package com.starlink.vpn.ui.login

import android.content.Intent
import android.os.Bundle
import android.text.InputType
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.starlink.vpn.MainActivity
import com.starlink.vpn.R
import com.starlink.vpn.databinding.ActivityLoginBinding
import com.starlink.vpn.viewmodel.VpnViewModel

class LoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLoginBinding
    private lateinit var vpnViewModel: VpnViewModel
    private var isPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        vpnViewModel = ViewModelProvider(this)[VpnViewModel::class.java]
        
        setupViews()
        setupClickListeners()
    }
    
    private fun setupViews() {
        // Set initial password visibility
        updatePasswordVisibility()
    }
    
    private fun setupClickListeners() {
        // Login button
        binding.loginButton.setOnClickListener {
            performLogin()
        }
        
        // Anonymous login
        binding.anonymousLogin.setOnClickListener {
            performAnonymousLogin()
        }
        
        // Password visibility toggle
        binding.passwordToggle.setOnClickListener {
            togglePasswordVisibility()
        }
        
        // Forgot password
        binding.forgotPassword.setOnClickListener {
            showToast("密码重置功能开发中")
        }
    }
    
    private fun performLogin() {
        val username = binding.usernameInput.text.toString().trim()
        val password = binding.passwordInput.text.toString().trim()
        
        if (username.isEmpty()) {
            showToast("请输入用户名")
            binding.usernameInput.requestFocus()
            return
        }
        
        if (password.isEmpty()) {
            showToast("请输入密码")
            binding.passwordInput.requestFocus()
            return
        }
        
        // Disable button during login
        binding.loginButton.isEnabled = false
        binding.loginButton.text = "登录中..."
        
        vpnViewModel.login(username, password) { success ->
            binding.loginButton.isEnabled = true
            binding.loginButton.text = getString(R.string.login_button)
            
            if (success) {
                showToast("登录成功")
                navigateToMain()
            } else {
                showToast("登录失败，请检查用户名和密码")
            }
        }
    }
    
    private fun performAnonymousLogin() {
        // Disable button during login
        binding.anonymousLogin.isEnabled = false
        
        vpnViewModel.anonymousLogin { success ->
            binding.anonymousLogin.isEnabled = true
            
            if (success) {
                showToast("匿名登录成功")
                navigateToMain()
            } else {
                showToast("匿名登录失败")
            }
        }
    }
    
    private fun togglePasswordVisibility() {
        isPasswordVisible = !isPasswordVisible
        updatePasswordVisibility()
    }
    
    private fun updatePasswordVisibility() {
        if (isPasswordVisible) {
            binding.passwordInput.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            binding.passwordToggle.setImageResource(R.drawable.ic_eye)
        } else {
            binding.passwordInput.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
            binding.passwordToggle.setImageResource(R.drawable.ic_eye_off)
        }
        
        // Move cursor to end
        binding.passwordInput.setSelection(binding.passwordInput.text.length)
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
        
        // Add transition animation
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left)
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
