<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.StarlinkVPN" parent="Theme.Material3.DayNight">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        
        <item name="colorSecondary">@color/primary_blue</item>
        <item name="colorSecondaryVariant">@color/primary_blue_dark</item>
        <item name="colorOnSecondary">@color/text_white</item>
        
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorOnBackground">@color/text_primary</item>
        
        <item name="colorSurface">@color/background_white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <item name="android:statusBarColor">@color/primary_blue</item>
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
    
    <style name="Theme.StarlinkVPN.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    
    <!-- Login Theme -->
    <style name="Theme.StarlinkVPN.Login" parent="Theme.StarlinkVPN.NoActionBar">
        <item name="android:windowBackground">@drawable/login_background</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
    
    <!-- Card Styles -->
    <style name="CardStyle">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:elevation">8dp</item>
        <item name="android:layout_margin">16dp</item>
    </style>
    
    <!-- Button Styles -->
    <style name="PrimaryButton">
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">16dp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@animator/button_elevation</item>
    </style>
    
    <style name="SecondaryButton">
        <item name="android:background">@drawable/button_secondary</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">16dp</item>
        <item name="android:elevation">2dp</item>
    </style>
    
    <style name="DangerButton">
        <item name="android:background">@drawable/button_danger</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:padding">16dp</item>
        <item name="android:elevation">4dp</item>
    </style>
    
    <!-- Text Styles -->
    <style name="TitleText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    
    <style name="SubtitleText">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    
    <style name="BodyText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>
    
    <style name="CaptionText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
    </style>
    
    <style name="HintText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_hint</item>
    </style>
    
    <!-- Input Styles -->
    <style name="InputStyle">
        <item name="android:background">@drawable/input_background</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
</resources>
