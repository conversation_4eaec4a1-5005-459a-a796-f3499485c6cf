<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/background_white"
            android:elevation="4dp"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/servers"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Premium Promotion -->
        <androidx.cardview.widget.CardView
            android:id="@+id/premium_promotion"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:elevation="8dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="8dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/premium_background"
                android:padding="20dp">

                <ImageView
                    android:id="@+id/premium_icon"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_gem"
                    android:tint="@color/text_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:id="@+id/premium_content"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:orientation="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/view_plans_button"
                    app:layout_constraintStart_toEndOf="@id/premium_icon"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="5dp"
                        android:text="@string/join_premium"
                        android:textColor="@color/text_white"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/access_all_servers"
                        android:textColor="@color/text_white"
                        android:textSize="14sp"
                        android:alpha="0.9" />

                </LinearLayout>

                <Button
                    android:id="@+id/view_plans_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/button_premium"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="10dp"
                    android:text="@string/view_plans"
                    android:textColor="@color/text_white"
                    android:textSize="14sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <!-- Server Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="20dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="15dp"
                android:paddingStart="10dp"
                android:text="@string/vip_channel"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <!-- Server List -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:elevation="8dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Smart Connect -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/server_smart"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/server_item_selected"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        android:padding="20dp">

                        <View
                            android:id="@+id/smart_flag"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="@drawable/server_flag_smart"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_globe"
                            android:tint="@color/text_white"
                            app:layout_constraintBottom_toBottomOf="@id/smart_flag"
                            app:layout_constraintEnd_toEndOf="@id/smart_flag"
                            app:layout_constraintStart_toStartOf="@id/smart_flag"
                            app:layout_constraintTop_toTopOf="@id/smart_flag" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginEnd="15dp"
                            android:text="@string/smart_connect"
                            android:textColor="@color/text_white"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/smart_status"
                            app:layout_constraintStart_toEndOf="@id/smart_flag"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/smart_status"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/connection_dot_connected"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- China HK -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/server_china_hk"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        android:padding="20dp">

                        <View
                            android:id="@+id/china_flag"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="@drawable/server_flag_china"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:gravity="center"
                            android:text="🇨🇳"
                            android:textSize="20sp"
                            app:layout_constraintBottom_toBottomOf="@id/china_flag"
                            app:layout_constraintEnd_toEndOf="@id/china_flag"
                            app:layout_constraintStart_toStartOf="@id/china_flag"
                            app:layout_constraintTop_toTopOf="@id/china_flag" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginEnd="15dp"
                            android:text="@string/china_hk"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/china_status"
                            app:layout_constraintStart_toEndOf="@id/china_flag"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/china_status"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/connection_dot_disconnected"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/divider" />

                    <!-- USA -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/server_usa"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground"
                        android:padding="20dp">

                        <View
                            android:id="@+id/usa_flag"
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:background="@drawable/server_flag_usa"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:gravity="center"
                            android:text="🇺🇸"
                            android:textSize="20sp"
                            app:layout_constraintBottom_toBottomOf="@id/usa_flag"
                            app:layout_constraintEnd_toEndOf="@id/usa_flag"
                            app:layout_constraintStart_toStartOf="@id/usa_flag"
                            app:layout_constraintTop_toTopOf="@id/usa_flag" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15dp"
                            android:layout_marginEnd="15dp"
                            android:text="@string/usa"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toStartOf="@id/usa_status"
                            app:layout_constraintStart_toEndOf="@id/usa_flag"
                            app:layout_constraintTop_toTopOf="parent" />

                        <View
                            android:id="@+id/usa_status"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:background="@drawable/connection_dot_disconnected"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
