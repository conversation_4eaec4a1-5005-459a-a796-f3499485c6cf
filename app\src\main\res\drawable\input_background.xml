<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/input_focused" />
            <stroke android:width="2dp" android:color="@color/input_border" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/input_background" />
            <stroke android:width="2dp" android:color="@color/transparent" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
