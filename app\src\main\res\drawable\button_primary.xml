<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_primary_pressed"
                android:endColor="@color/button_primary_pressed"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="@color/button_primary"
                android:endColor="@color/button_primary"
                android:type="linear" />
            <corners android:radius="12dp" />
        </shape>
    </item>
</selector>
